import 'package:page/src/features/models/content.dart';

import '../../features/models/video_model.dart';

class HomeResponse {
  final List<VideoModel> featuredvideo;
  final List<VideoModel> category;
  final List<VideoModel> luxury;
  final List<VideoModel> places;
  final List<VideoModel> holidayHomes;
  final List<VideoModel> carRentals;
  final List<Types> types;

  final String error;
  final int code;
  final String msg;

  HomeResponse.fromJson(Map parsedJson)
      : featuredvideo = parsedJson['data']['featured_videos'] != null
            ? (parsedJson['data']['featured_videos'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        category = parsedJson['data']['recent_videos'] != null
            ? (parsedJson['data']['recent_videos'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        luxury = parsedJson['data']['properties'] != null
            ? (parsedJson['data']['properties'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        places = parsedJson['data']['places'] != null
            ? (parsedJson['data']['places'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        holidayHomes = parsedJson['data']['holiday_homes'] != null
            ? (parsedJson['data']['holiday_homes'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        carRentals = parsedJson['data']['car_rentals'] != null
            ? (parsedJson['data']['car_rentals'] as List)
                .map((p) => VideoModel.fromJson(p))
                .toList()
            : [],
        types = parsedJson['data']['types'] != null
            ? (parsedJson['data']['types'] as List)
                .map((p) => Types.fromJson(p))
                .toList()
            : [],
        error = "",
        code = 1,
        msg = "success";

  HomeResponse.withError(String errorValue)
      : featuredvideo = [],
        category = [],
        luxury = [],
        types = [],
        places = [],
        holidayHomes = [],
        carRentals = [],
        error = errorValue,
        code = 0,
        msg = "fail";
}
