import 'dart:developer';

import '../../core/services/api.dart';
import '../models/content.dart';
import '../models/features.dart';
import '../models/locations.dart';

class ContentController {
  static final List<Locations> locations = [];
  static final List<Features> features = [];
  static final List<Types> types = [];
  static final List<Agents> agents = [];
  static final List<Sizes> sizes = [];
  static final List<Sizes> holidayRoomSizes = [];
  static final List<Sizes> propertiesRoomSizes = [];
  static final List<Brands> brands = [];
  static final List<Years> years = [];
  dynamic maxprice;
  dynamic minprice;

  Future<void> getlocations() async {
    await Api.getlocations().then((value) {
      locations.clear();
      locations.addAll(value.city);
    });
  }

  Future<void> getfeatures(String category) async {
    await Api.getfeatures(category).then((value) {
      features.clear();
      features.addAll(value.features);
    });
  }

  getminprice(String category) async {
    await Api.getminpricemaincategory(category).then((value) {
      log('Dataaaa ${value.data}');
      minprice = value.data;
    });
  }

  getmaxprice(String category) async {
    await Api.getmaxpricemaincategory(category).then((value) {
      log('Dataaaa222 ${value.data}');
      maxprice = value.data;
    });
  }

  Future<void> gettypes(String category) async {
    await Api.gettypes(category).then((value) {
      types.clear();
      types.addAll(value.types);
      log('Typess: ${types.length}');
    });
  }

  getagents() async {
    await Api.getagents().then((value) {
      agents.clear();
      agents.addAll(value.agents);
    });
  }

  Future<void> getagentsbycategory(String category) async {
    await Api.getagentsbycategory(category).then((value) {
      agents.clear();
      agents.addAll(value.agents);
    });
  }

  Future<void> getsizes() async {
    await Api.getsizes().then((value) {
      sizes.clear();
      sizes.addAll(value.sizes);
    });
  }

  Future<void> getsizesAndRooms() async {
    await Api.getsizesAndRooms().then((value) {
      holidayRoomSizes.clear();
      propertiesRoomSizes.clear();
      holidayRoomSizes.addAll(value.holidayRoomSizes);
      propertiesRoomSizes.addAll(value.propertiesRoomSizes);
      log('sakjasdhs ${holidayRoomSizes.length}}');
    });
  }

  getbrands() async {
    await Api.getbrands().then((value) {
      log('Braasdasdnds: ${value.brands.length}');
      brands.clear();
      brands.addAll(value.brands);
    });
  }

  getyears() async {
    await Api.getyears().then((value) {
      years.clear();
      years.addAll(value.years);
    });
  }

  getmaxpricecarrent() async {
    await Api.getmaxpricecarrent().then((value) {
      maxprice = value.data;
    });
  }
}
