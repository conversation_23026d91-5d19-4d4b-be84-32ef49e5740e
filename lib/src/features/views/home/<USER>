import 'package:flutter/material.dart';
import 'package:page/src/core/shared_widgets/main_featured_container.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/views/holiday/holiday.dart';
import 'package:page/src/features/views/home/<USER>/home_categories_section.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
import 'package:video_player/video_player.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/shared_widgets/bottom_navgation_bar.dart';
import '../../../core/utils/dynamic_links.dart';
import 'widgets/featured_videos_section/featured_videos.dart';
import 'widgets/recently_added_section/recently_added_section.dart';

Map<int?, VideoPlayerController?> videoPlayerController = {};
// ValueNotifier<Map<int?, VideoPlayerController?>> videoPlayerControllerN =
//     ValueNotifier({});
Map<int?, VideoPlayerController?> reelsVideoController = {};

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  _HomeState createState() => _HomeState();
}

class _HomeState extends State<Home> {
  int code = 0;
  String msg = 'loading';

  AuthController authController = AuthController();

  final splashServices = SplashServices();

  @override
  void initState() {
    super.initState();
    // splashServices.initControllersForHomeData();
    DynamicLinkHandler.initDynamicLink();
  }

  // @override
  // void dispose() {
  //   SplashServices().disposeHomeVideoControllers();
  //   super.dispose();
  // }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      body: SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          //! Top Section
          const HomeCategoriesTopSection(),

          // code == 0 && msg == 'loading'
          //     ? buildLoadingWidget()
          //     : code == 1
          //         ?
          Column(children: [
            //! Featured Videos Section
            MainFeaturedContainer(
              label: 'featuredHolidayVideos',
              child: _MainHomeTile(
                  label: 'FeturedVideos',
                  section: HomeFeaturedVideos(
                      featuredvideos: featuredVideos,
                      categorylist: categoryList)),
            ),

            const SizedBox(
              height: 20,
            ),

            //! Recently Added Section
            _MainHomeTile(
                label: 'recentlyadded',
                icon: const Icon(
                  Icons.timelapse_outlined,
                ),
                section: HomeRecentlyAdded(
                  categorylist: categoryList,
                )),

            const SizedBox(
              height: 20,
            ),

            const HolidayHomeWidgetSection(
              fromHome: true,
            ),

            const SizedBox(
              height: 20,
            ),
            //! Holiday Home Section
            // _MainHomeTile(
            //   label: 'holidayhome',
            //   icon: Image.asset(
            //     'assets/home2.png',
            //     color: Colors.black,
            //     height: 20,
            //     fit: BoxFit.cover,
            //   ),
            //   section: HolidayHomeSectionVideos(
            //     holidayHomes: holidayHomes,
            //   ),
            // ),
            //
            // // const SizedBox(
            // //   height: 20,
            // // ),
            //
            // //! Car Rental Section
            // // _MainHomeTile(
            // //   icon: Image.asset(
            // //     'assets/icons8-car.png',
            // //     color: Colors.black,
            // //     height: 20,
            // //     fit: BoxFit.cover,
            // //   ),
            // //   label: 'carrental',
            // //   section: CarRentalSectionVideos(
            // //     carRentals: carRentals,
            // //   ),
            // // ),
            //
            // const SizedBox(
            //   height: 20,
            // ),
            //
            // //! Places Section
            // _MainHomeTile(
            //     label: 'Areas',
            //     icon: Image.asset(
            //       'assets/icons/destination.png',
            //       color: Colors.black,
            //       height: 20,
            //       fit: BoxFit.cover,
            //     ),
            //     section: HomePlacesSection(
            //       places: places,
            //     )),
            //
            // const SizedBox(
            //   height: 20,
            // ),
            //
            // //! Properties Section
            // _MainHomeTile(
            //   label: 'Properties',
            //   icon: Image.asset(
            //     'assets/icons/home.png',
            //     color: Colors.black,
            //     height: 20,
            //   ),
            //   section: HomePropertySection(luxury: properties),
            // ),
          ])
          // : buildErrorWidget(msg)
        ]),
      ),
      bottomNavigationBar: CustomBottomNavgationBar(0),
    ));
  }
}

class _MainHomeTile extends StatelessWidget {
  const _MainHomeTile({
    Key? key,
    required this.label,
    required this.section,
    this.icon,
  }) : super(key: key);

  final String label;
  final Widget section;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    final isFeatured = label == 'FeturedVideos';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Row(
            children: [
              if (icon != null) ...[
                icon!,
                const SizedBox(
                  width: 10,
                ),
              ],
              if (!isFeatured)
                Text(
                  AppLocalizations.of(context).translate(label),
                  style: const TextStyle(
                      color: Color(0xff51565B),
                      fontSize: 17,
                      fontWeight: FontWeight.bold),
                ),
            ],
          ),
        ),
        if (!isFeatured)
          const SizedBox(
            height: 20,
          ),
        section,
      ],
    );
  }
}
