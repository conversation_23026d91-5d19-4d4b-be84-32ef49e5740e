import 'dart:async';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/ad_details/services/cached_video_services.dart';
import 'package:page/src/features/views/home/<USER>';
import 'package:video_player/video_player.dart';

import '../../../models/content.dart';

List<Types> allTypes = [];
List<VideoModel> featuredVideos = [];
List<VideoModel> categoryList = [];
List<VideoModel> properties = [];
List<VideoModel> places = [];
List<VideoModel> holidayHomes = [];
List<VideoModel> carRentals = [];

class SplashServices {
  static final _cacheVideoServices = CachedVideoServices();

// @pragma('vm:entry-point')
//! Init Video Controllers ====================================
  Future<void> initControllersForHomeData() async {
    final allVideos = [
      ...featuredVideos,
      ...categoryList,
      ...properties,
      ...holidayHomes,
      ...carRentals,
      ...places,
    ];

    //! Init Videos ====================================
    await Future.forEach(allVideos, (video) async {
      try {
        String videoUrl = video.video ?? '';

        if (!kDebugMode) {
          await setVideosToMap(
            videoUrl: videoUrl,
          );
        }
      } on Exception catch (e) {
        log('Error=> $e');
      }
    });
  }

  //! Set Videos to Map ====================================
  Future<void> setVideosToMap({
    required String videoUrl,
  }) async {
    final checkIfVideoCached = await _cacheVideoServices.checkVideoIfCached(
      videoUrl,
    );

    if (checkIfVideoCached == null) {
      await Future.microtask(() => _cacheVideoServices.cacheVideo(videoUrl));
    }
  }

  //! Dispose
  void disposeHomeVideoControllers() {
    for (var video in videoPlayerController.values) {
      video?.pause();
      video?.dispose();
      video = null;
    }
    videoPlayerController.clear();
  }

  // Future<void> disposeHomeVideoControllers() async {
  //   await Future.forEach(videoPlayerController.values, (element) async {
  //     await element?.dispose();
  //     element?.value = const VideoPlayerValue.uninitialized();
  //   });
  //   // videoPlayerController.clear();
  // }

  //! Dispose
  Future<void> disposeHomeVideoControllersExceptOneVideo({
    required int videoId,
  }) async {
    videoPlayerController.forEach((key, value) {
      if (key != videoId) {
        value?.value = const VideoPlayerValue.uninitialized();
      }
    });
  }

//! Get Home Data
  Future<void> getHomeData() async {
    final value = await Api.gethome();
    featuredVideos = value.featuredvideo;
    categoryList = value.category.length > 10
        ? value.category.sublist(0, 9)
        : value.category;
    properties = value.luxury;
    places = value.places;
    holidayHomes = value.holidayHomes;
    carRentals = value.carRentals;
    allTypes = value.types;

    print('sfasfsafsa ${allTypes.map((e) => e.name)}');

    initControllersForHomeData();

    // getReels(20);
  }

//! Get Reels
//   void getReels(int size) async {
//     try {
//       final value = await Api.getreels(size);
//
//       if (value != null) {
//         stories.clear();
//
//         stories.addAll(value.category);
//
//         // for (var story in stories) {
//         //   _splashServices.setReelsToMap(
//         //       videoUrl: story.video!,
//         //       videoId: story.id!,
//         //       videoPlayerController: reelsVideoController);
//         // }
//       }
//     } catch (e) {
//       log('Error: $e');
//     }
//   }
}
