import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:page/src/core/shared_widgets/bottom_navgation_bar.dart';
import 'package:page/src/core/utils/print_services.dart';

import '../../../core/localization/app_localizations.dart';
import '../account/account.dart';

String? categoryFilter;

class NewProjectWidget extends StatefulWidget {
  final bool fromBottomNav;

  const NewProjectWidget({Key? key, this.fromBottomNav = false})
      : super(key: key);

  @override
  _NewProjectWidget createState() => _NewProjectWidget();
}

class _NewProjectWidget extends State<NewProjectWidget> {
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const Account()),
          );

          return Future.value(false);
        },
        child: Safe<PERSON>rea(
            child: Scaffold(
          bottomNavigationBar: CustomBottomNavgationBar(3),
          appBar: AppBar(
            automaticallyImplyLeading: widget.fromBottomNav ? false : true,
            backgroundColor: const Color(0xFF27b4a8),
            centerTitle: true,
            title: Text(
              AppLocalizations.of(context).translate('Projects'),
              style: TextStyle(
                fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
              ),
            ),
          ),
          // coming soon
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(CupertinoIcons.info_circle_fill,
                    color: Color(0xff233549), size: 120),
                const SizedBox(height: 20),
                Text(
                  '${AppLocalizations.of(context).translate('Coming Soon')}...',
                  style: TextStyle(
                    fontFamily: isEng(context) ? 'Roboto' : 'Tajawal',
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF27b4a8),
                  ),
                ),
              ],
            ),
          ),
        )));
  }
}
