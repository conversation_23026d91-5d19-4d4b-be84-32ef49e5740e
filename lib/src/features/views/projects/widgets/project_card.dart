import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/localization/app_language.dart';
import '../../ad_details/video_widget.dart';

class ProjectCard extends StatelessWidget {
  final VideoModel project;
  final String? date;
  final String? time;
  final int? planId;

  const ProjectCard({
    Key? key,
    required this.project,
    this.date,
    this.time,
    this.planId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var lang =
        Provider.of<AppLanguage>(context, listen: false).appLocal.languageCode;
    final isEnglish = lang == 'en';

    // Get the first project plan for bedrooms and price info
    final firstPlan = project.projectPlans?.isNotEmpty == true
        ? project.projectPlans!.first
        : null;

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (BuildContext context) => VideoViewWidget(
            video: project,
            planId: planId,
            date: date,
            time: time,
          ),
        ));
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image section (half of card height)
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: Container(
                height: 150,
                width: double.infinity,
                child: project.images?.isNotEmpty == true
                    ? MainCachedImage(
                        imageUrl: project.images!,
                        fit: BoxFit.cover,
                      )
                    : Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.image,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
              ),
            ),
            // Content section
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Project name
                  Text(
                    project.name ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  // Three icons with values
                  Row(
                    children: [
                      // Location icon
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              size: 16,
                              color: primaryColor,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                project.locationName ?? '',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Price from icon
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Icons.attach_money,
                              size: 16,
                              color: primaryColor,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                firstPlan?.priceFrom?.isNotEmpty == true
                                    ? '${AppLocalizations.of(context).translate('Price From')} ${firstPlan!.priceFrom}'
                                    : project.startprice != null
                                        ? '${project.startprice}'
                                        : '',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Bedrooms icon
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Icons.bed,
                              size: 16,
                              color: primaryColor,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                firstPlan?.bedrooms?.isNotEmpty == true
                                    ? '${firstPlan!.bedrooms} ${AppLocalizations.of(context).translate('Bedrooms')}'
                                    : project.rooms ?? '',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
